# Utility Commons

This module contains common utility classes and functions used across the Illumio Data platform.

## FlowDataValidator

The `FlowDataValidator` is a utility class for validating JSON strings against the Flow Data schema used in the Illumio Data platform.

### Features

- **JSON Format Validation**: Ensures the input is valid JSON
- **Required Fields Validation**: Checks that all required fields are present
- **Data Type Validation**: Validates that each field has the correct data type
- **Unrecognized Fields Detection**: Reports error for fields not in the schema
- **DateTime Format Validation**: Supports multiple datetime formats including ISO 8601
- **Null Value Handling**: Allows null values for optional fields
- **Structured Error Reporting**: Returns detailed error codes and messages

### Usage

```java
import com.illumio.data.util.FlowDataValidator;
import com.illumio.data.util.FlowDataValidator.ValidationResult;

// Validate a JSON string
String jsonString = """
    {
        "SrcIP": "*************",
        "DestIP": "*********",
        "Port": 443,
        "Proto": "TCP",
        "SentBytes": 2048,
        "ReceivedBytes": 4096,
        "IllumioTenantId": "tenant-abc-123",
        "StartTime": "2023-12-01T14:30:00.123Z",
        "EndTime": "2023-12-01T14:35:00.456Z"
    }
    """;

ValidationResult result = FlowDataValidator.validate(jsonString);

if (result.isValid()) {
    System.out.println("JSON is valid!");
} else {
    System.out.println("JSON validation failed!");
    System.out.println("Error Code: " + result.getErrorCode());
    System.out.println("Error Message: " + result.getDetailMessage());
}
```

### Schema

The validator supports the following Flow Data schema:

#### Required Fields
- `SrcIP` (String) - Source IP address
- `DestIP` (String) - Destination IP address  
- `Port` (Integer) - Port number
- `Proto` (String) - Protocol
- `SentBytes` (Long) - Number of bytes sent
- `ReceivedBytes` (Long) - Number of bytes received
- `IllumioTenantId` (String) - Illumio tenant identifier
- `StartTime` (DateTime) - Flow start time
- `EndTime` (DateTime) - Flow end time

#### Optional Fields
The validator supports over 80 optional fields including:
- Network identifiers (SrcId, DestId, etc.)
- Geographic information (SrcCountry, DestCountry, etc.)
- Cloud provider information (SrcCloudProvider, DestCloudProvider, etc.)
- Threat intelligence data (ThreatLevel, ThreatDescription, etc.)
- Device information (DeviceName, DeviceVendor, etc.)
- And many more...

#### Supported Data Types
- **String**: Text fields like IP addresses, names, descriptions
- **Integer**: Numeric fields like Port, ThreatLevel, ProcessId
- **Long**: Large numeric fields like SentBytes, ReceivedBytes
- **Boolean**: True/false fields like IsWellknown flags
- **Double**: Decimal fields like geographic coordinates
- **DateTime**: Timestamp fields supporting multiple formats

#### Supported DateTime Formats
- `yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'` (ISO 8601 with microseconds)
- `yyyy-MM-dd'T'HH:mm:ss.SSS'Z'` (ISO 8601 with milliseconds)
- `yyyy-MM-dd'T'HH:mm:ss'Z'` (ISO 8601 basic)
- `yyyy-MM-dd'T'HH:mm:ss` (ISO 8601 without timezone)
- `yyyy-MM-dd HH:mm:ss.SSSSSS` (Space-separated with microseconds)
- `yyyy-MM-dd HH:mm:ss.SSS` (Space-separated with milliseconds)
- `yyyy-MM-dd HH:mm:ss` (Space-separated basic)

### Return Value

The `validate` method returns a `ValidationResult` object containing:
- **isValid()**: `true` if the JSON is valid, `false` otherwise
- **getErrorCode()**: Structured error code if validation fails, `null` if validation succeeds
- **getDetailMessage()**: Detailed error message if validation fails, `null` if validation succeeds

### Error Codes

The validator provides structured error codes for different validation failures:
- **INVALID_JSON**: Input is null, empty, or not valid JSON
- **INVALID_JSON_FORMAT**: JSON parsing failed
- **INVALID_JSON_TYPE**: Input is not a JSON object
- **MISS_REQUIRED_FIELD_[FIELDNAME]**: Required field is missing (e.g., `MISS_REQUIRED_FIELD_SRCIP`)
- **WRONG_TYPE_[FIELDNAME]**: Field has wrong data type (e.g., `WRONG_TYPE_PORT`)
- **UNRECOGNIZED_FIELD_[FIELDNAME]**: Field is not in the schema (e.g., `UNRECOGNIZED_FIELD_UNKNOWNFIELD`)

### Legacy Support

For backward compatibility, a legacy method is available:
```java
// Deprecated - use validate(String) instead
Pair<Boolean, String> result = FlowDataValidator.validateLegacy(jsonString);
```

### Testing

The validator includes comprehensive unit tests covering:
- Valid JSON scenarios
- Invalid JSON scenarios
- Missing required fields
- Wrong data types
- Invalid datetime formats
- Null value handling
- Edge cases

Run tests with:
```bash
./gradlew :commons:utility-commons:test --tests FlowDataValidatorTest
```

### Dependencies

The validator uses the following dependencies:
- Jackson for JSON parsing
- Apache Commons Lang for Pair utility
- SLF4J for logging
- Lombok for utility class annotation
