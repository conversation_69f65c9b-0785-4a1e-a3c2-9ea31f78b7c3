package com.illumio.data.util;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Example usage of FlowDataValidator.
 */
class FlowDataValidatorExampleTest {

    @Test
    void demonstrateValidatorUsage() {
        // Example 1: Valid JSON
        String validJson = """
            {
                "SrcIP": "*************",
                "DestIP": "*********",
                "Port": 443,
                "Proto": "TCP",
                "SentBytes": 2048,
                "ReceivedBytes": 4096,
                "IllumioTenantId": "tenant-abc-123",
                "StartTime": "2023-12-01T14:30:00.123Z",
                "EndTime": "2023-12-01T14:35:00.456Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(validJson);
        assertTrue(result.getLeft());
        assertNull(result.getRight());
        System.out.println("Valid JSON: " + result.getLeft());
        
        // Example 2: Invalid JSON - missing required field
        String invalidJson1 = """
            {
                "SrcIP": "*************",
                "DestIP": "*********",
                "Port": 443,
                "Proto": "TCP",
                "SentBytes": 2048,
                "ReceivedBytes": 4096
            }
            """;
        
        result = FlowDataValidator.validate(invalidJson1);
        assertFalse(result.getLeft());
        assertNotNull(result.getRight());
        System.out.println("Invalid JSON (missing field): " + result.getRight());
        
        // Example 3: Invalid JSON - wrong data type
        String invalidJson2 = """
            {
                "SrcIP": "*************",
                "DestIP": "*********",
                "Port": "four-four-three",
                "Proto": "TCP",
                "SentBytes": 2048,
                "ReceivedBytes": 4096,
                "IllumioTenantId": "tenant-abc-123",
                "StartTime": "2023-12-01T14:30:00.123Z",
                "EndTime": "2023-12-01T14:35:00.456Z"
            }
            """;
        
        result = FlowDataValidator.validate(invalidJson2);
        assertFalse(result.getLeft());
        assertNotNull(result.getRight());
        System.out.println("Invalid JSON (wrong type): " + result.getRight());
        
        // Example 4: Invalid JSON - malformed JSON
        String malformedJson = """
            {
                "SrcIP": "*************",
                "DestIP": "*********",
                "Port": 443,
                "Proto": "TCP"
                // Missing comma and closing brace
            """;
        
        result = FlowDataValidator.validate(malformedJson);
        assertFalse(result.getLeft());
        assertNotNull(result.getRight());
        System.out.println("Malformed JSON: " + result.getRight());
    }
}
