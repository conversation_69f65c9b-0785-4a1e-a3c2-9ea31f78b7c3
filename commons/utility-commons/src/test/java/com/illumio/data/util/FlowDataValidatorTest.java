package com.illumio.data.util;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for FlowDataValidator.
 */
class FlowDataValidatorTest {

    @Test
    void testValidateNullJson() {
        Pair<Boolean, String> result = FlowDataValidator.validate(null);
        assertFalse(result.getLeft());
        assertEquals("JSON string is null or empty", result.getRight());
    }

    @Test
    void testValidateEmptyJson() {
        Pair<Boolean, String> result = FlowDataValidator.validate("");
        assertFalse(result.getLeft());
        assertEquals("JSON string is null or empty", result.getRight());
    }

    @Test
    void testValidateInvalidJson() {
        Pair<Boolean, String> result = FlowDataValidator.validate("{invalid json");
        assertFalse(result.getLeft());
        assertTrue(result.getRight().startsWith("Invalid JSON format:"));
    }

    @Test
    void testValidateJsonArray() {
        Pair<Boolean, String> result = FlowDataValidator.validate("[]");
        assertFalse(result.getLeft());
        assertEquals("JSON must be an object", result.getRight());
    }

    @Test
    void testValidateMissingRequiredFields() {
        String json = "{}";
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertFalse(result.getLeft());
        assertTrue(result.getRight().startsWith("Missing required field:"));
    }

    @Test
    void testValidateValidMinimalJson() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertTrue(result.getLeft(), "Expected valid JSON but got error: " + result.getRight());
        assertNull(result.getRight());
    }

    @Test
    void testValidateCompleteValidJson() {
        String json = """
            {
                "SrcIP": "***********",
                "SrcId": "src-123",
                "CSSrcId": "cs-src-123",
                "DestIP": "********",
                "DestId": "dest-123",
                "CSDestId": "cs-dest-123",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "SrcTenantId": "src-tenant-123",
                "SrcSubId": "src-sub-123",
                "SrcRegion": "us-east-1",
                "SrcResId": "src-res-123",
                "SrcVnetId": "src-vnet-123",
                "SrcUserName": "user1",
                "DestTenantId": "dest-tenant-123",
                "DestSubId": "dest-sub-123",
                "DestRegion": "us-west-1",
                "DestResId": "dest-res-123",
                "DestVnetId": "dest-vnet-123",
                "DestUserName": "user2",
                "SrcFlowType": "internal",
                "DestFlowType": "external",
                "SrcThreatLevel": 1,
                "DestThreatLevel": 2,
                "SrcIsWellknown": true,
                "DestIsWellknown": false,
                "SrcDomain": "example.com",
                "DestDomain": "target.com",
                "SrcCountry": "US",
                "DestCountry": "CA",
                "SrcCity": "New York",
                "DestCity": "Toronto",
                "SrcCloudProvider": "AWS",
                "DestCloudProvider": "Azure",
                "MaliciousIPLatitude": 40.7128,
                "MaliciousIPLongitude": -74.0060,
                "FlowCount": 5,
                "PacketsReceived": 10,
                "PacketsSent": 8,
                "StartTime": "2023-01-01T10:00:00.123456Z",
                "EndTime": "2023-01-01T10:05:00.654321Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertTrue(result.getLeft(), "Expected valid JSON but got error: " + result.getRight());
        assertNull(result.getRight());
    }

    @Test
    void testValidateInvalidStringField() {
        String json = """
            {
                "SrcIP": 123,
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertFalse(result.getLeft());
        assertEquals("Field 'SrcIP' must be a string", result.getRight());
    }

    @Test
    void testValidateInvalidIntegerField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": "eighty",
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertFalse(result.getLeft());
        assertEquals("Field 'Port' must be an integer", result.getRight());
    }

    @Test
    void testValidateInvalidLongField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": "not-a-number",
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertFalse(result.getLeft());
        assertEquals("Field 'SentBytes' must be a long", result.getRight());
    }

    @Test
    void testValidateInvalidBooleanField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "SrcIsWellknown": "yes",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertFalse(result.getLeft());
        assertEquals("Field 'SrcIsWellknown' must be a boolean", result.getRight());
    }

    @Test
    void testValidateInvalidDoubleField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "MaliciousIPLatitude": "not-a-number",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertFalse(result.getLeft());
        assertEquals("Field 'MaliciousIPLatitude' must be a number", result.getRight());
    }

    @Test
    void testValidateInvalidDateTimeField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "invalid-date",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertFalse(result.getLeft());
        assertEquals("Field 'StartTime' has invalid datetime format", result.getRight());
    }

    @Test
    void testValidateVariousDateTimeFormats() {
        String[] validDateTimes = {
            "2023-01-01T10:00:00.123456Z",
            "2023-01-01T10:00:00.123Z",
            "2023-01-01T10:00:00Z",
            "2023-01-01T10:00:00",
            "2023-01-01 10:00:00.123456",
            "2023-01-01 10:00:00.123",
            "2023-01-01 10:00:00"
        };
        
        for (String dateTime : validDateTimes) {
            String json = String.format("""
                {
                    "SrcIP": "***********",
                    "DestIP": "********",
                    "Port": 80,
                    "Proto": "TCP",
                    "SentBytes": 1024,
                    "ReceivedBytes": 2048,
                    "IllumioTenantId": "tenant-123",
                    "StartTime": "%s",
                    "EndTime": "%s"
                }
                """, dateTime, dateTime);
            
            Pair<Boolean, String> result = FlowDataValidator.validate(json);
            assertTrue(result.getLeft(), 
                "Expected valid datetime format '" + dateTime + "' but got error: " + result.getRight());
        }
    }

    @Test
    void testValidateWithNullOptionalFields() {
        String json = """
            {
                "SrcIP": "***********",
                "SrcId": null,
                "DestIP": "********",
                "DestId": null,
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        Pair<Boolean, String> result = FlowDataValidator.validate(json);
        assertTrue(result.getLeft(), "Expected valid JSON with null optional fields but got error: " + result.getRight());
        assertNull(result.getRight());
    }
}
