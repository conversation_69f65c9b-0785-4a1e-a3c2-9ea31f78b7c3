package com.illumio.data.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Utility class for validating JSON strings against the Flow Data schema.
 * 
 * This validator checks for:
 * - Valid JSON format
 * - Required fields presence
 * - Correct data types for each field
 * - Valid datetime format for StartTime and EndTime
 * 
 * <AUTHOR> Data Team
 */
@Slf4j
@UtilityClass
public class FlowDataValidator {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * Validation result containing validation status, error code, and detailed message.
     */
    @Data
    @AllArgsConstructor
    public static class ValidationResult {
        private final boolean valid;
        private final String errorCode;
        private final String detailMessage;

        /**
         * Creates a successful validation result.
         */
        public static ValidationResult success() {
            return new ValidationResult(true, null, null);
        }

        /**
         * Creates a failed validation result.
         */
        public static ValidationResult failure(String errorCode, String detailMessage) {
            return new ValidationResult(false, errorCode, detailMessage);
        }
    }
    
    // Required fields according to the schema
    private static final Set<String> REQUIRED_FIELDS = new HashSet<>(Arrays.asList(
        "SrcIP", "DestIP", "Port", "Proto", "SentBytes", "ReceivedBytes", 
        "IllumioTenantId", "StartTime", "EndTime"
    ));
    
    // String fields
    private static final Set<String> STRING_FIELDS = new HashSet<>(Arrays.asList(
        "SrcIP", "SrcId", "CSSrcId", "DestIP", "DestId", "CSDestId", "Proto",
        "IllumioTenantId", "SrcTenantId", "SrcSubId", "SrcRegion", "SrcResId",
        "SrcVnetId", "SrcUserName", "DestTenantId", "DestSubId", "DestRegion",
        "DestResId", "DestVnetId", "DestUserName", "SrcFlowType", "DestFlowType",
        "SrcDeviceId", "SrcFirewallId", "SrcUserId", "DestDeviceId", "DestFirewallId",
        "DestUserId", "SrcResourceType", "DestResourceType", "SrcDomain", "DestDomain",
        "SrcCountry", "DestCountry", "SrcCity", "DestCity", "SrcCloudProvider",
        "DestCloudProvider", "SrcGeoRegion", "DestGeoRegion", "SourceHostName",
        "SourceMACAddress", "SourceNTDomain", "SourceProcessName", "SourceUserName",
        "SourceUserPrivileges", "DeviceAction", "DeviceAddress", "DestinationDnsDomain",
        "DestinationHostName", "DestinationMACAddress", "DestinationNTDomain",
        "DestinationProcessName", "DestinationServiceName", "DestinationTranslatedAddress",
        "DestinationUserName", "DestinationUserPrivileges", "LogSeverity", "MaliciousIP",
        "MaliciousIPCountry", "LAWTenantId", "ThreatConfidence", "ThreatDescription",
        "ThreatSeverity", "SourceDnsDomain", "SourceServiceName", "SourceSystem",
        "DeviceMacAddress", "DeviceName", "DeviceOutboundInterface", "DeviceProduct",
        "DeviceVendor", "DeviceTranslatedAddress", "DeviceVersion", "DeviceTimeZone",
        "DeviceExternalID", "ReceiptTime", "Activity", "AdditionalExtensions",
        "SourceZone", "DestinationZone", "RequestURL", "Computer", "SrcCloudTags",
        "DestCloudTags", "TrafficStatus", "SourceLabel", "DestinationLabel",
        "SrcAccountName", "DestAccountName", "SrcResourceCategory", "DestResourceCategory",
        "SrcResourceName", "DestResourceName", "SrcResourceGroup", "DestResourceGroup",
        "SrcSubnetId", "DestSubnetId", "SrcCSLabel", "DestCSLabel", "DeviceExternalId",
        "DestinationExternalLabel", "DestinationExternalLabelCategory", "SourceExternalLabel",
        "SourceExternalLabelCategory", "SrcCSLabels", "DestCSLabels", "Hops", "DeniedAt"
    ));
    
    // Integer fields
    private static final Set<String> INTEGER_FIELDS = new HashSet<>(Arrays.asList(
        "Port", "SrcThreatLevel", "DestThreatLevel", "SourceProcessId", "DestinationProcessId",
        "DeviceCustomNumber3", "FlowCount", "PacketsReceived", "PacketsSent"
    ));
    
    // Long fields
    private static final Set<String> LONG_FIELDS = new HashSet<>(Arrays.asList(
        "SentBytes", "ReceivedBytes"
    ));
    
    // Boolean fields
    private static final Set<String> BOOLEAN_FIELDS = new HashSet<>(Arrays.asList(
        "SrcIsWellknown", "DestIsWellknown"
    ));
    
    // Double fields
    private static final Set<String> DOUBLE_FIELDS = new HashSet<>(Arrays.asList(
        "MaliciousIPLatitude", "MaliciousIPLongitude"
    ));
    
    // DateTime fields
    private static final Set<String> DATETIME_FIELDS = new HashSet<>(Arrays.asList(
        "StartTime", "EndTime"
    ));

    // All valid fields (combination of all field types)
    private static final Set<String> ALL_VALID_FIELDS = new HashSet<>();

    static {
        ALL_VALID_FIELDS.addAll(STRING_FIELDS);
        ALL_VALID_FIELDS.addAll(INTEGER_FIELDS);
        ALL_VALID_FIELDS.addAll(LONG_FIELDS);
        ALL_VALID_FIELDS.addAll(BOOLEAN_FIELDS);
        ALL_VALID_FIELDS.addAll(DOUBLE_FIELDS);
        ALL_VALID_FIELDS.addAll(DATETIME_FIELDS);
    }
    
    /**
     * Validates a JSON string against the Flow Data schema.
     *
     * @param jsonString the JSON string to validate
     * @return ValidationResult containing validation status, error code, and detailed message
     */
    public static ValidationResult validate(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return ValidationResult.failure("INVALID_JSON", "JSON string is null or empty");
        }

        JsonNode jsonNode;
        try {
            jsonNode = OBJECT_MAPPER.readTree(jsonString);
        } catch (Exception e) {
            log.debug("Failed to parse JSON: {}", e.getMessage());
            return ValidationResult.failure("INVALID_JSON_FORMAT", "Invalid JSON format: " + e.getMessage());
        }

        if (!jsonNode.isObject()) {
            return ValidationResult.failure("INVALID_JSON_TYPE", "JSON must be an object");
        }

        // Check for unrecognized fields first
        ValidationResult unrecognizedFieldResult = checkUnrecognizedFields(jsonNode);
        if (!unrecognizedFieldResult.isValid()) {
            return unrecognizedFieldResult;
        }

        // Check required fields
        for (String requiredField : REQUIRED_FIELDS) {
            if (!jsonNode.has(requiredField) || jsonNode.get(requiredField).isNull()) {
                return ValidationResult.failure(
                    "MISS_REQUIRED_FIELD_" + requiredField.toUpperCase(),
                    "Missing required field: " + requiredField
                );
            }
        }

        // Validate field types
        return validateFieldTypes(jsonNode);
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use validate(String) which returns ValidationResult instead
     */
    @Deprecated
    public static Pair<Boolean, String> validateLegacy(String jsonString) {
        ValidationResult result = validate(jsonString);
        return new ImmutablePair<>(result.isValid(), result.getDetailMessage());
    }
    
    /**
     * Checks for unrecognized fields in the JSON node.
     */
    private static ValidationResult checkUnrecognizedFields(JsonNode jsonNode) {
        try {
            jsonNode.fieldNames().forEachRemaining(fieldName -> {
                if (!ALL_VALID_FIELDS.contains(fieldName)) {
                    throw new ValidationException(
                        "UNRECOGNIZED_FIELD_" + fieldName.toUpperCase(),
                        "Unrecognized field: " + fieldName
                    );
                }
            });
            return ValidationResult.success();
        } catch (ValidationException e) {
            return ValidationResult.failure(e.getErrorCode(), e.getMessage());
        }
    }

    /**
     * Validates the data types of all fields in the JSON node.
     */
    private static ValidationResult validateFieldTypes(JsonNode jsonNode) {
        try {
            jsonNode.fieldNames().forEachRemaining(fieldName -> {
                JsonNode fieldValue = jsonNode.get(fieldName);

                if (fieldValue.isNull()) {
                    return; // Null values are allowed for optional fields
                }

                // Validate field types
                validateSingleField(fieldName, fieldValue);
            });

            return ValidationResult.success();
        } catch (ValidationException e) {
            return ValidationResult.failure(e.getErrorCode(), e.getMessage());
        }
    }
    
    /**
     * Validates a single field's data type.
     */
    private static void validateSingleField(String fieldName, JsonNode fieldValue) {
        if (STRING_FIELDS.contains(fieldName)) {
            if (!fieldValue.isTextual()) {
                throw new ValidationException(
                    "WRONG_TYPE_" + fieldName.toUpperCase(),
                    "Field '" + fieldName + "' must be a string"
                );
            }
        } else if (INTEGER_FIELDS.contains(fieldName)) {
            if (!fieldValue.isInt()) {
                throw new ValidationException(
                    "WRONG_TYPE_" + fieldName.toUpperCase(),
                    "Field '" + fieldName + "' must be an integer"
                );
            }
        } else if (LONG_FIELDS.contains(fieldName)) {
            if (!fieldValue.isIntegralNumber()) {
                throw new ValidationException(
                    "WRONG_TYPE_" + fieldName.toUpperCase(),
                    "Field '" + fieldName + "' must be a long"
                );
            }
        } else if (BOOLEAN_FIELDS.contains(fieldName)) {
            if (!fieldValue.isBoolean()) {
                throw new ValidationException(
                    "WRONG_TYPE_" + fieldName.toUpperCase(),
                    "Field '" + fieldName + "' must be a boolean"
                );
            }
        } else if (DOUBLE_FIELDS.contains(fieldName)) {
            if (!fieldValue.isNumber()) {
                throw new ValidationException(
                    "WRONG_TYPE_" + fieldName.toUpperCase(),
                    "Field '" + fieldName + "' must be a number"
                );
            }
        } else if (DATETIME_FIELDS.contains(fieldName)) {
            if (!fieldValue.isTextual()) {
                throw new ValidationException(
                    "WRONG_TYPE_" + fieldName.toUpperCase(),
                    "Field '" + fieldName + "' must be a datetime string"
                );
            }
            if (!isValidDateTime(fieldValue.asText())) {
                throw new ValidationException(
                    "WRONG_TYPE_" + fieldName.toUpperCase(),
                    "Field '" + fieldName + "' has invalid datetime format"
                );
            }
        }
    }
    
    /**
     * Validates if a string represents a valid datetime.
     * Supports ISO 8601 format and common datetime patterns.
     */
    private static boolean isValidDateTime(String dateTimeString) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            return false;
        }
        
        // Common datetime patterns to try
        String[] patterns = {
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss'Z'",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd HH:mm:ss.SSSSSS",
            "yyyy-MM-dd HH:mm:ss.SSS",
            "yyyy-MM-dd HH:mm:ss"
        };
        
        for (String pattern : patterns) {
            try {
                DateTimeFormatter.ofPattern(pattern).parse(dateTimeString);
                return true;
            } catch (DateTimeParseException e) {
                // Try next pattern
            }
        }
        
        return false;
    }
    
    /**
     * Internal exception class for validation errors.
     */
    private static class ValidationException extends RuntimeException {
        private final String errorCode;

        public ValidationException(String errorCode, String message) {
            super(message);
            this.errorCode = errorCode;
        }

        public String getErrorCode() {
            return errorCode;
        }
    }
}
