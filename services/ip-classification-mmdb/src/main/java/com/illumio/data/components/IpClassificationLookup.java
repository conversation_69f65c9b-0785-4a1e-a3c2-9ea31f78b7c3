package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import mmdb.Mmdb.BluePipelineIPCRequest;
import mmdb.Mmdb.BluePipelineIPCResponse;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

import java.net.InetAddress;
import java.net.UnknownHostException;

@Component
@Slf4j
@RequiredArgsConstructor
public class IpClassificationLookup {
    // Flow Schema keys - https://confluence.illum.io/x/ptPgEg
    public static final String SrcIP = "SrcIP";
    public static final String SrcThreatLevel = "SrcThreatLevel";
    public static final String SrcIsWellknown = "SrcIsWellknown";
    public static final String SrcDomain = "SrcDomain";
    public static final String SrcCountry = "SrcCountry";
    public static final String SrcCity = "SrcCity";
    public static final String SrcGeoRegion = "SrcGeoRegion";
    public static final String SrcCloudProvider = "SrcCloudProvider";
    public static final String DestIP = "DestIP";
    public static final String DestThreatLevel = "DestThreatLevel";
    public static final String DestIsWellknown = "DestIsWellknown";
    public static final String DestDomain = "DestDomain";
    public static final String DestCountry = "DestCountry";
    public static final String DestCity = "DestCity";
    public static final String DestGeoRegion = "DestGeoRegion";
    public static final String DestCloudProvider = "DestCloudProvider";
    public static final String DestinationExternalLabel = "DestinationExternalLabel";
    public static final String DestinationExternalLabelCategory = "DestinationExternalLabelCategory";
    public static final String SourceExternalLabel = "SourceExternalLabel";
    public static final String SourceExternalLabelCategory = "SourceExternalLabelCategory";

    public static final String IllumioTenantId = "IllumioTenantId";

    // Helper objects
    private final ObjectMapper objectMapper;
    private final BlueMmdbClient blueMmdbClient;

    // Metrics
    private final LongCounter mismatchFieldsCounter;
    private final LongCounter insightIPClassificationEventCounter;


    private void addCountMetricsPerTenant(JsonNode resourceIdFlow) {
        try {
            // Extract IllumioTenantId value
            String illumioTenantId = resourceIdFlow.get(IllumioTenantId).asText();

            insightIPClassificationEventCounter.add(1, Attributes.builder().put("insight_IPC_tenant_id", illumioTenantId).build());
        } catch (Exception e) {
            log.error("Error extracting IllumioTenantId for record : {}", resourceIdFlow, e);
            insightIPClassificationEventCounter.add(1, Attributes.builder().put("insight_IPC_tenant_id", "Unknown").build());
        }
    }

    public Mono<JsonNode> maybeAddIpClassification(JsonNode resourceIdFlow) {
        this.addCountMetricsPerTenant(resourceIdFlow);
        
        if (!resourceIdFlow.isObject()) {
            log.debug("Didn't find an object node, not processing resourceIdFlow {}", resourceIdFlow);
            return Mono.just(resourceIdFlow);
        }

        ObjectNode resourceIdFlowObject = (ObjectNode) resourceIdFlow;

        if (!resourceIdFlowObject.has(SrcIP) || !resourceIdFlowObject.has(DestIP)) {
            log.debug("Missing IP fields in resourceIdFlow {}", resourceIdFlow);
            return Mono.just(resourceIdFlowObject);
        }

        BluePipelineIPCRequest request = BluePipelineIPCRequest.newBuilder()
                .setSrcIP(resourceIdFlowObject.get(SrcIP).asText())
                .setDestIP(resourceIdFlowObject.get(DestIP).asText())
                .build();

        return blueMmdbClient.getBluePipelineIPC(request)
                .map(resp -> addBluePipelineIPCInfo(resourceIdFlowObject, resp))
                .onErrorResume(throwable -> {
                    if (throwable instanceof io.github.resilience4j.circuitbreaker.CallNotPermittedException ||
                            throwable instanceof java.util.concurrent.TimeoutException) {
                        log.debug("Circuit breaker is open, returning original flow without enrichment", throwable);
                        return Mono.just(resourceIdFlowObject);
                    }
                    return Mono.error(throwable);
                });
    }

    public JsonNode addBluePipelineIPCInfo(JsonNode resourceIdFlow, BluePipelineIPCResponse resp) {
        ObjectNode node = (ObjectNode) resourceIdFlow;

        JsonNode srcIpNode = node.get(SrcIP);
        JsonNode destIpNode = node.get(DestIP);
        String srcIp = srcIpNode != null ? srcIpNode.asText() : null;
        String destIp = destIpNode != null ? destIpNode.asText() : null;

        // Populate source information
        addFieldIfExists(
                node, SrcThreatLevel, resp.getSrcThreatLevel(), resp.hasSrcThreatLevel());
        addFieldIfExists(node, SrcIsWellknown,
                srcIp != null && !isPrivateIP(srcIp) && resp.getSrcIsWellknown(),
                resp.hasSrcIsWellknown() || (srcIp != null && isPrivateIP(srcIp)));
        addFieldIfExists(node, SrcDomain, resp.getSrcDomain());
        addFieldIfExists(node, SrcCountry, resp.getSrcCountry());
        addFieldIfExists(node, SrcCity, resp.getSrcCity());
        addFieldIfExists(node, SrcGeoRegion, resp.getSrcRegion());
        addFieldIfExists(node, SrcCloudProvider, resp.getSrcCloudProvider());
        addFieldIfExists(node, SourceExternalLabel, resp.getSourceExternalLabel());
        addFieldIfExists(node, SourceExternalLabelCategory, resp.getSourceExternalLabelCategory());

        // Populate destination information
        addFieldIfExists(
                node, DestThreatLevel, resp.getDestThreatLevel(), resp.hasDestThreatLevel());
        addFieldIfExists(node, DestIsWellknown,
                destIp != null && !isPrivateIP(destIp) && resp.getDestIsWellknown(),
                resp.hasDestIsWellknown() || (destIp != null && isPrivateIP(destIp)));
        addFieldIfExists(node, DestDomain, resp.getDestDomain());
        addFieldIfExists(node, DestCountry, resp.getDestCountry());
        addFieldIfExists(node, DestCity, resp.getDestCity());
        addFieldIfExists(node, DestGeoRegion, resp.getDestRegion());
        addFieldIfExists(node, DestCloudProvider, resp.getDestCloudProvider());
        addFieldIfExists(node, DestinationExternalLabel, resp.getDestinationExternalLabel());
        addFieldIfExists(node, DestinationExternalLabelCategory, resp.getDestinationExternalLabelCategory());

        return node;
    }

    private void addFieldIfExists(ObjectNode node, String fieldName, String value) {
        if (value == null) return;
        if (value.isEmpty()) return;
        if (node.has(fieldName) && !(node.get(fieldName).isNull() || node.get(fieldName).asText().isEmpty()) && !node.get(fieldName).asText().equals(value)) {
            mismatchFieldsCounter.add(
                    1, Attributes.builder().put("fieldName", fieldName).build());
            return; // do not decorate if there is a mismatch, keep the upstream values.
        }
        node.set(fieldName, objectMapper.valueToTree(value));
    }

    private void addFieldIfExists(
            ObjectNode node, String fieldName, Object value, boolean fieldExists) {
        if (!fieldExists) return;

        if (node.has(fieldName) && !(node.get(fieldName).isNull() || node.get(fieldName).asText().isEmpty()) && !node.get(fieldName).equals(objectMapper.valueToTree(value))) {
            mismatchFieldsCounter.add(
                    1, Attributes.builder().put("fieldName", fieldName).build());
            return; // do not decorate if there is a mismatch, keep the upstream values.
        }
        node.set(fieldName, objectMapper.valueToTree(value));
    }

    private boolean isPrivateIP(String ipAddress) {
        try {
            InetAddress addr = InetAddress.getByName(ipAddress);
            return addr.isSiteLocalAddress() || addr.isLinkLocalAddress() || addr.isLoopbackAddress();
        } catch (UnknownHostException e) {
            log.debug("IP unknown: {}", ipAddress, e);
            return false;
        }
    }
}
